import { FxElement, html } from '/fx.js';
import { $styles } from './jlink.x.js';
import '../button/button.js';
import '../jcode/jcode.js';
import '../jmd/jmd.js';
import '../jepub/jepub.js';
import '../jdoc/jdoc.js';

export class FxJLink extends FxElement {
    static properties = {
        base: { type: Object },
        isReady: { type: Boolean, default: false },
        mediaPath: { type: String, default: 'http://192.168.192.2:8001' },
        cell: { type: Object, default: undefined, notify: true },
        url: { type: String, default: '', notify: true },
        ext: { type: String, default: '...' },
        pdfjsView: { type: Boolean, default: false, save: true },
        pdfjsViewUrl: { type: String, default: 'http://192.168.192.2:8001/~apps~/pdf.js/web/viewer.html?file=' },
        maxSize: { type: Number, default: 20 * 1024 * 1024 }, // 20MB
        errorMessage: { type: String, default: '' }
    }
    get is_pdf() {
        return this.ext === 'pdf' || this.cell?.url?.endsWith('.pdf') || this.cell?.attachment?.endsWith('.pdf');
    }
    get is_html() {
        return this.ext === 'html' || this.cell?.url?.endsWith('.html')
    }
    get iframeSrc() {
        if (this.is_pdf && this.pdfjsView) {
            if (this.isAttachments) {
                return '/fx/~/pdf.js/web/viewer.html?file=' + this.checkedURL;
            }
            const viewer = FX.$url.replace('fx.js', 'fx/~/pdf.js/web/viewer.html?file=')
            let url = this.isLoadFile ? viewer : (this.pdfjsViewUrl || viewer)
            url += this.checkedURL;
            return url;
        }
        return this.checkedURL;
    }
    get isAttachments() { return this.cell?.attachment || this.cell?.attachments }

    async firstUpdated() {
        super.firstUpdated();
        setTimeout(async () => {
            await this.init();
            this.isReady = true;
            this.$update();
            setTimeout(() => {
                this.style.opacity = 1;
            }, 100)
        }, 100)
    }

    'url-changed'(e) {
        if (!e) return;
        if (this.isReady && !this.isAttachments && !this.isLoadFile && this._lastUrl !== e) {
            this._lastUrl = e;
            this.linkTypeByUrl(e);
            this.checkedURL = FX.checkUrl(e);
            this.$update();
        }
    }

    async init() {
        if (this.isAttachments) {
            let db = this.db = this.base.dbLocal,
                _id = 'info:' + this.base.fxSelected._id,
                doc, blob;
            try { doc = await db.getAttachment(_id, this.cell.ulid) } catch (error) { }
            if (!doc) {
                _id = this._idFile = this.cell?._idFile || 'file:' + this.base.bsSelected._id + '-' + this.cell.ulid;
                try { doc = await db.getAttachment(_id, 'file') } catch (error) { }
            }
            if (doc) {
                this.linkTypeByUrl(this.cell.attachment);
                const arrayBuffer = await doc.arrayBuffer();
                blob = new Blob([arrayBuffer], { type: doc.type });
                this.checkedURL = URL.createObjectURL(blob, { type: 'data:text' });
                this.$update();
            }
        } else {
            let url = this.cell?.source || this.cell?.url || this.url;
            this.linkTypeByUrl(url);
            this.checkedURL = FX.checkUrl(url);
            this.$update();
        }
    }

    async loadFile(e) {
        const file = e.target?.files[0];
        if (!file) return;
        if (file.size > this.maxSize) {
            const fileSizeMB = (file.size / 1024 / 1024).toFixed(2);
            const maxSizeMB = (this.maxSize / 1024 / 1024).toFixed(2);
            await FX.showModal({
                ok: 'Ok',
                modal: '400, 180',
                label: 'File Size Error',
                info1: `File size (${fileSizeMB} MB) exceeds maximum allowed size (${maxSizeMB} MB)`,
                info2: `Размер файла (${fileSizeMB} МБ) превышает максимально допустимый размер (${maxSizeMB} МБ)`
            });
            e.target.value = '';
            return;
        }
        this.linkTypeByUrl(file.name);
        // if (!this.isHTML && this.cell?.cell_type === 'html' && this.cell?.source) // ???
        //     return;
        this.isLoadFile = true;
        this.url = file.name;
        if (this.cell) {
            this.cell.size = file.size;
            this.cell.url = file.name;
        }
        const reader = new FileReader();
        reader.onload = async (e) => {
            let url = e.target.result;
            let res = await fetch(url);
            let blob = await res.blob();
            if (this.base)
                this.base.showLoader = false;
            await this.saveAttachment(blob, null, file.name);
        }
        if (this.base)
            this.base.showLoader = true;
        reader.readAsDataURL(file);
    }
    async saveAttachment(blob, isEdited, name) {
        if (this.linkType === 'file')
            this.linkTypeByMimeType(blob.type);
        let url = URL.createObjectURL(blob);
        this.checkedURL = url;
        if (this.cell && this.base?.fxSelected?._id) {
            this.cell.attachment = name;
            this.cell.url = isEdited ? 'edited attached image' : this.url; // ???
            this.base.notebook._attachments ||= {};
            this.base.notebook._attachments[this.cell.ulid] = {
                content_type: blob.type,
                data: blob
            }
        }
        this.$update();
    }
    async deleteFile(deleteAttachment = false) {
        if (this.cell) {
            this.cell.url = this.cell.size = this.cell.source = this.cell.ext = '';
            this.cell.attachment = false;
            if (deleteAttachment) {
                this.base.toDeleteAttachments ||= [];
                this.base.toDeleteAttachments.push({ _id: 'info:' + this.base.fxSelected._id, ulid: this.cell.ulid, _rev: null });
                delete this.base.notebook._attachments?.[this.cell.ulid];
            }
            delete this.cell.url;
            delete this.cell.ext;
            delete this.cell.source;
            delete this.cell._idFile;
            delete this.cell.size;
            delete this.cell.attachment;
        }
        this.url = this.ext = this.linkType = this.checkedURL = '';
        this.isLoadFile = false;
        this.$update();
    }

    async renderTextContent() {
        if (!this.checkedURL) return '';
        try {
            const response = await fetch(this.checkedURL);
            if (!response.ok) throw new Error('Failed to fetch text content');
            const text = await response.text();
            const maxLength = 100 * 1024; // 100KB
            if (text.length > maxLength) {
                return text.substring(0, maxLength) + '\n\n... (content truncated)';
            }
            return text;
        } catch (error) {
            console.error('Error loading text content:', error);
            return 'Error loading text content';
        }
    }

    openInNewTab() {
        let url = (this.pdfjsView ? this.iframeSrc : this.checkedURL) || '';
        window.open(url, '_blank').focus();
    }

    static styles = [$styles]

    get get_Main() {
        if (!this.checkedURL && !this.errorMessage || !this.isReady) return null;
        if (this.linkType === 'error') {
            return html`
                <div class="error-container" style="padding: 20px; text-align: center; color: red;">
                    <h3>Error Loading File</h3>
                    <p>${this.errorMessage || 'Unknown error occurred'}</p>
                </div>
            `
        }
        return html`
            <div class="vertical flex w100 h100 relative overflow-y box ${this.cell?.docClass || ''}" style="${this.cell?.docStyle || ''}">
                ${(this.linkType === 'file' || !this.linkType || this.linkType === 'html') ? html`
                    <iframe .src=${this.iframeSrc || this.checkedURL} style="height: 100%; border: none; overflow: auto; min-height: 0px;"></iframe>
                ` : html``}
                ${this.linkType === 'image' ? html`
                    <img class="box" src=${this.checkedURL} style="min-height: 0; height: 100%; width: 100%; object-fit: contain;">
                ` : html``}
                ${this.linkType === 'video' ? html`
                    <div class="relative w100 h100">
                        <video src=${this.checkedURL} controls style="position: absolute; top: 6px; left: 0; width: 100%; height: calc(100% - 6px);"></video>
                    </div>
                ` : html``}
                ${this.linkType === 'audio' ? html`
                    <audio src=${this.checkedURL} controls style="padding: 4px; display: flex; margin: auto;"></audio>
                ` : html``}
                ${this.linkType === 'text' ? html`
                    <div class="text-viewer" style="padding: 10px; font-family: monospace; white-space: pre-wrap; overflow: auto; height: 100%;">
                        ${this.renderTextContent()}
                    </div>
                ` : html``}
                ${this.linkType === 'code' ? html`
                    <fx-jcode .url=${this.checkedURL} .cell=${this.cell} readOnly style="height: 100%;"></fx-jcode>
                ` : html``}
                ${this.linkType === 'document' ? html`
                    <fx-jdoc .url=${this.checkedURL} .cell=${this.cell} .base=${this.base} style="height: 100%;"></fx-jdoc>
                ` : html``}
                ${this.linkType === 'archive' ? html`
                    <div class="archive-viewer" style="padding: 10px;">
                        <h3>Archive Contents</h3>
                        <p>Archive viewing functionality will be implemented here</p>
                        <a href=${this.checkedURL} download>Download Archive</a>
                    </div>
                ` : html``}
                ${this.linkType === 'model3d' ? html`
                    <div class="model3d-viewer" style="padding: 10px; text-align: center;">
                        <h3>3D Model Viewer</h3>
                        <p>3D model viewing functionality will be implemented here</p>
                        <a href=${this.checkedURL} download>Download Model</a>
                    </div>
                ` : html``}
                ${this.linkType === 'markdown' ? html`
                    <fx-jmd class="block p10" .src=${this.checkedURL} style="position: absolute; left: 0; width: 100%;"></fx-jmd>
                ` : html``}
                ${this.linkType === 'epub' ? html`
                    <fx-jepub .src=${this.checkedURL} style="position: absolute; left: 0; width: 100%;"></fx-jepub>
                ` : html``}
            </div>
        `
    }
    render() {
        return html`
            ${this.get_Main}
        `
    }

    static fileTypeConfig = {
        file: ['pdf'],
        image: ['webp', 'png', 'jpg', 'jpeg', 'gif', 'bmp', 'svg', 'apng', 'ico', 'tiff', 'tif'],
        video: ['mp4', 'mkv', 'ogv', 'webm', 'mov', 'avi', 'wmv', 'flv', '3gp', 'm4v'],
        audio: ['mp3', 'wav', 'flac', 'ogg', 'aac', 'm4a', 'wma', 'opus'],
        document: ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'odt', 'ods', 'odp'],
        code: ['js', 'ts', 'py', 'java', 'cpp', 'c', 'h', 'css', 'scss', 'less', 'php', 'rb', 'go', 'rs', 'swift', 'kt', 'json'],
        text: ['txt', 'json', 'xml', 'csv', 'log', 'yaml', 'yml', 'ini', 'cfg', 'conf'],
        archive: ['zip', 'rar', '7z', 'tar', 'gz', 'bz2'],
        model3d: ['obj', 'stl', 'gltf', 'glb', 'fbx', 'dae', 'ply'],
        html: ['html', 'htm'],
        markdown: ['md', 'markdown'],
        epub: ['epub']

    }
    linkTypeByUrl(url) {
        if (!this.is_html && this.cell?.cell_type === 'html' && this.cell?.source) {
            const text = this.cell?.source;
            const blob = new Blob([text], { type: 'text/html' });
            const blobURL = URL.createObjectURL(blob);
            this.checkedURL = blobURL;
            this.linkType = 'html';
            return;
        }
        let ext = url?.split('.').pop().toLowerCase();
        this.linkType = 'file';
        if (!ext) return;
        for (const [type, extensions] of Object.entries(FxJLink.fileTypeConfig)) {
            if (extensions.includes(ext)) {
                this.linkType = type;
                return;
            }
        }
    }

    static mimeTypeConfig = {
        file: ['application/pdf'],
        image: ['image/'],
        video: ['video/'],
        audio: ['audio/'],
        document: [
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-powerpoint',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation'
        ],
        text: ['text/plain', 'application/json', 'text/xml', 'text/csv'],
        archive: ['application/zip', 'application/x-rar-compressed', 'application/x-7z-compressed'],
        html: ['text/html'],
        markdown: ['text/markdown', 'text/x-markdown'],
        epub: ['application/epub+zip']
    }
    linkTypeByMimeType(type = file) {
        this.linkType = 'file';
        for (const [fileType, mimeTypes] of Object.entries(FxJLink.mimeTypeConfig)) {
            for (const mimeType of mimeTypes) {
                if (type.includes(mimeType)) {
                    this.linkType = fileType;
                    this['is_' + fileType];
                    return;
                }
            }
        }
    }
}

customElements.define('fx-jlink', FxJLink);

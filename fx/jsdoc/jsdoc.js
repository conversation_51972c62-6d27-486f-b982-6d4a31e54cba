import { FxElement, html, css } from '/fx.js';

import '../jtmp/jtmp.js';

const libPath = import.meta.url.split('/').slice(0, -1).join('/') + '/lib/';

customElements.define('fx-jsdoc', class FxJsdoc extends FxElement {
    static properties = {
        src: { type: String },
        cell: { type: Object },
        base: { type: Object },
        // on_resize_delay: { type: String, default: '100' },
        // resize_fire_name: { type: String, default: 'resize-splitter-jsdoc' },
    }

    loadFile(file) {
        console.log('loadFile', file);
    //     if (file) {
    //         // Создаем URL для файла
    //         const fileUrl = URL.createObjectURL(file);

    //         if (this.isReady) {
    //             const iframe = this.$qs('#editor')?.shadowRoot?.querySelector('iframe');
    //             if (iframe && iframe.contentWindow) {
    //                 iframe.contentWindow.postMessage({
    //                     type: 'loadFile',
    //                     fileUrl: fileUrl,
    //                     fileName: file.name
    //                 }, '*');
    //             }
    //         } else {
    //             // Сохраняем файл для загрузки после готовности
    //             this.pendingFile = { url: fileUrl, name: file.name };
    //         }

    //         // Сохраняем информацию о файле в ячейке
    //         if (this.cell) {
    //             this.cell.fileName = file.name;
    //             this.cell.fileSize = file.size;
    //             this.cell.fileType = file.type;
    //         }
    //     }
    }

    render() {
        return html`
            <fx-jtmp id="editor" .src=${this.src} .srcdoc=${this.srcdoc} editMode on_resize_delay=${this.on_resize_delay} resize_fire_name=${this.resize_fire_name}></fx-jtmp>
        `
    }

    srcdoc(src) {
        const initialContent = this.cell?.source || this.documentContent || '';
        return `
<!DOCTYPE html>
<html lang="en">
<head>
  <link rel="stylesheet" href="${libPath}style.css">
  <script type="module" src="${libPath}superdoc.umd.js"></script>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SuperDoc Editor</title>
  <style>
    body {
        margin: 0;
        padding: 0;
        display: flex;
        flex-direction: column;
        height: 100vh;
        background: white;
    }
    .superdoc__layers.layers {
        margin: 0 auto;
    }
  </style>
</head>
<body>
  <div id="my-toolbar"></div>
  <div id="superdoc"></div>

  <script type="module">
    let superdoc = null;
    let isReady = false;

    const config = {
        selector: '#superdoc',
        toolbar: '#my-toolbar',
        documentMode: 'editing',
        pagination: true,
        rulers: true,
        onReady: (event) => {
            console.log('SuperDoc is ready', event);
            isReady = true;
        },
        onEditorCreate: (event) => {
            console.log('Editor is created', event);
        },
        onDocumentChange: (event) => {
            console.log('Document changed', event);
        }
    }
    superdoc = new SuperDocLibrary.SuperDoc(config);

  </script>

</body>
</html>
        `
    }
})
